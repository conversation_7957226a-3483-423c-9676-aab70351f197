<?php
include('db.php');

// ตรวจสอบการส่งข้อมูลคำถาม
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $survey_id = $_POST['survey_id'];
    $question_text = $_POST['question_text'];
    $question_type = $_POST['question_type'];

    // เพิ่มคำถามลงในฐานข้อมูล
    $sql = "INSERT INTO questions (survey_id, question_text, question_type) 
            VALUES ('$survey_id', '$question_text', '$question_type')";

    if ($conn->query($sql) === TRUE) {
        echo "คำถามถูกเพิ่มเรียบร้อย";
    } else {
        echo "Error: " . $sql . "<br>" . $conn->error;
    }
}

// ดึงข้อมูลแบบประเมินทั้งหมด
$sql = "SELECT * FROM surveys";
$result = $conn->query($sql);
?>

<form method="POST" action="add_question.php">
    <div class="form-group">
        <label for="survey_id">เลือกแบบประเมิน:</label>
        <select class="form-control" name="survey_id" required>
            <?php while($row = $result->fetch_assoc()) { ?>
                <option value="<?php echo $row['survey_id']; ?>"><?php echo $row['title']; ?></option>
            <?php } ?>
        </select>
    </div>
    <div class="form-group">
        <label for="question_text">คำถาม:</label>
        <textarea class="form-control" name="question_text" required></textarea>
    </div>
    <div class="form-group">
        <label for="question_type">ประเภทคำถาม:</label>
        <select class="form-control" name="question_type" required>
            <option value="multiple_choice">ตัวเลือก</option>
            <option value="rating">ให้คะแนน</option>
            <option value="open_ended">ข้อความเปิด</option>
        </select>
    </div>
    <button type="submit" class="btn btn-primary">เพิ่มคำถาม</button>
</form>
