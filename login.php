<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Teacher Evaluation System</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card mt-5">
                    <div class="card-body">
                        <h3 class="text-center">ເຂົ້າສູ່ລະບົບ</h3>
                        <?php
                        // Include database connection
                        include('db.php');
                        
                        // Initialize session
                        session_start();
                        
                        // Check if form is submitted
                        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
                            $username = $_POST['username'];
                            $password = $_POST['password'];
                            
                            // Query to check user credentials
                            $sql = "SELECT * FROM users WHERE username = '$username'";
                            $result = $conn->query($sql);
                            
                            if ($result->num_rows == 1) {
                                $user = $result->fetch_assoc();
                                // Verify password (consider using password_hash/password_verify in production)
                                if ($password == $user['password']) {
                                    // Set session variables
                                    $_SESSION['user_id'] = $user['user_id'];
                                    $_SESSION['username'] = $user['username'];
                                    $_SESSION['role'] = $user['role'];
                                    
                                    // Redirect to dashboard
                                    header("Location: Dashboard.php");
                                    exit();
                                } else {
                                    echo "<div class='alert alert-danger'>ລະຫັດຜ່ານບໍ່ຖືກຕ້ອງ</div>";
                                }
                            } else {
                                echo "<div class='alert alert-danger'>ບໍ່ພົບຊື່ຜູ້ໃຊ້</div>";
                            }
                        }
                        ?>
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                            <div class="form-group">
                                <label for="username">ຊື້ຜູ້ໃຊ້</label>
                                <input type="text" class="form-control" id="username" name="username" placeholder="ເຕີມຊື່ຜູ້ໃຊ້" required>
                            </div>
                            <div class="form-group">
                                <label for="password">ລະຫັດຜ່ານ</label>
                                <input type="password" class="form-control" id="password" name="password" placeholder="ເຕີມລະຫັດຜ່ານ" required>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">ເຂົ້າສູ່ລະບົບ</button>
                            <div class="text-center mt-2">
                                <a href="#">ລືມລະຫັດຜ່ານ?</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</body>
</html>
