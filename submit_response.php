<?php
// เชื่อมต่อกับฐานข้อมูล
include('db.php');

// ตรวจสอบการส่งข้อมูลแบบฟอร์ม
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // ดึงค่าจากฟอร์มที่ผู้ใช้กรอก
    $survey_id = 1;  // กำหนด survey_id แบบตัวอย่าง
    $user_id = 123;  // กำหนด user_id แบบตัวอย่าง (สามารถดึงจาก session หรือฐานข้อมูล)
    $response_id = uniqid();  // สร้างรหัสการตอบ

    // บันทึกการตอบแบบสอบถาม
    $sql = "INSERT INTO responses (survey_id, user_id) VALUES ('$survey_id', '$user_id')";
    if ($conn->query($sql) === TRUE) {
        $response_id = $conn->insert_id; // รหัสการตอบที่เพิ่งถูกเพิ่ม
    } else {
        echo "Error: " . $conn->error;
    }

    // บันทึกคำตอบสำหรับคำถามต่าง ๆ
    // คำถามที่ 1 (Multiple Choice)
    $q1_answer = $_POST['q1'];
    $sql = "INSERT INTO answers (response_id, question_id, answer_text) 
            VALUES ('$response_id', 1, '$q1_answer')";
    $conn->query($sql);

    // คำถามที่ 2 (Rating)
    $q2_answer = $_POST['q2'];
    $sql = "INSERT INTO answers (response_id, question_id, answer_text) 
            VALUES ('$response_id', 2, '$q2_answer')";
    $conn->query($sql);

    // คำถามที่ 3 (Open-ended)
    $q3_answer = $_POST['q3'];
    $sql = "INSERT INTO answers (response_id, question_id, answer_text) 
            VALUES ('$response_id', 3, '$q3_answer')";
    $conn->query($sql);

    // เสร็จสิ้นการบันทึกคำตอบ
    echo "ขอบคุณที่กรอกแบบสอบถาม";
} else {
    echo "กรุณากรอกข้อมูลแบบสอบถาม";
}
?>


