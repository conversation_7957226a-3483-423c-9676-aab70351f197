<?php
// Include database connection
include('db.php');

// Initialize session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if user is a teacher
$sql = "SELECT * FROM users WHERE user_id = '$user_id' AND role = 'teacher'";
$result = $conn->query($sql);

if ($result->num_rows != 1) {
    // Redirect non-teachers to appropriate dashboard
    header("Location: student_dashboard.php");
    exit();
}

// Get courses taught by this teacher
// $courses_sql = "SELECT * FROM courses WHERE teacher_id = '$user_id'";
// $courses_result = $conn->query($courses_sql);

// Get recent evaluations
// $evaluations_sql = "SELECT s.title, COUNT(r.response_id) as response_count, AVG(r.rating) as avg_rating 
//                    FROM surveys s 
//                    JOIN responses r ON s.survey_id = r.survey_id 
//                    WHERE s.teacher_id = '$user_id' 
//                    GROUP BY s.survey_id 
//                    ORDER BY s.created_at DESC 
//                    LIMIT 5";
// $evaluations_result = $conn->query($evaluations_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - Teacher Evaluation System</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">ระบบ<lemma>การสอน</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item active">
                        <a class="nav-link" href="Dashboard.php">หน้า<lemma></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="User_Profile.php">โปรไฟล์</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">ออกจากระบบ</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="jumbotron">
                    <h2>ส<lemmaส<lemma, <?php echo $username; ?></h2>
                    <p><lemmaต้อน<lemma<|im_start|>่ระบบ<lemmaการสอน<lemmaอาจารย์</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-book mr-2"></i>ราย<lemma<lemmaสอน</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($courses_result && $courses_result->num_rows > 0): ?>
                            <ul class="list-group">
                                <?php while($course = $courses_result->fetch_assoc()): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <?php echo $course['course_name']; ?>
                                        <a href="course_details.php?id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-outline-primary"><lemma<lemma</a>
                                    </li>
                                <?php endwhile; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-muted">ไม่พบราย<lemma<lemmaสอน</p>
                        <?php endif; ?>
                        <a href="add_course.php" class="btn btn-success btn-block mt-3">
                            <i class="fas fa-plus mr-2"></i><lemma่มราย<lemmaใหม่
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar mr-2"></i>ผลการ<lemmaล่า<lemma</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($evaluations_result && $evaluations_result->num_rows > 0): ?>
                            <div class="list-group">
                                <?php while($eval = $evaluations_result->fetch_assoc()): ?>
                                    <div class="list-group-item">
                                        <h6><?php echo $eval['title']; ?></h6>
                                        <div class="d-flex justify-content-between">
                                            <small>จำนวน<lemma้ตอบ: <?php echo $eval['response_count']; ?></small>
                                            <small>คะแนนเฉ<lemma่ย: <?php echo number_format($eval['avg_rating'], 2); ?>/5</small>
                                        </div>
                                        <div class="progress mt-2">
                                            <div class="progress-bar" role="progressbar" 
                                                style="width: <?php echo ($eval['avg_rating']/5)*100; ?>%" 
                                                aria-valuenow="<?php echo $eval['avg_rating']; ?>" 
                                                aria-valuemin="0" aria-valuemax="5">
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted"><lemmaไม่<lemmaผลการ<lemma</p>
                        <?php endif; ?>
                        <a href="view_all_evaluations.php" class="btn btn-info btn-block mt-3">
                            <i class="fas fa-list mr-2"></i><lemmaผลการ<lemma<|im_start|>้งหมด
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-tasks mr-2"></i>เครื�อง<lemma</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="create_survey.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-plus-circle mr-2"></i>สร้างแบบ<lemmaใหม่
                            </a>
                            <a href="manage_surveys.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-edit mr-2"></i><lemmaแบบ<lemma
                            </a>
                            <a href="export_results.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-file-export mr-2"></i>ส่งออกผลการ<lemma
                            </a>
                            <a href="analytics.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-chart-line mr-2"></i><lemmaห์ผลการ<lemma
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0"><i class="fas fa-bell mr-2"></i>การแจ้ง<lemma</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i><lemma<|im_start|>вещаแบบ<lemmaใหม่ 5 คน
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>แบบ<lemma<|im_start|>"การ<lemmaโปรแกรมเว็บ" จะ<lemma้น<lemmaใน<lemma 3<lemma
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt mr-2"></i><lemmaทิน</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">กำหนดการ<lemma</p>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                เปิดแบบ<lemmaภาค<lemma<|im_start|>่ 1
                                <span class="badge badge-primary">15 ต.ค.</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <lemmaแบบ<lemmaภาค<lemma<|im_start|>่ 1
                                <span class="badge badge-danger">30 ต.ค.</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white mt-5 py-3">
        <div class="container text-center">
            <p class="mb-0">© 2023 ระบบ<lemmaการสอน. สงวน<lemmaข<lemma์.</p>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</body>
</html>
