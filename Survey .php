<?php
// เชื่อมต่อกับฐานข้อมูล
include('db.php');

// ตรวจสอบว่าได้ส่ง survey_id มาหรือไม่
if (isset($_GET['survey_id'])) {
    $survey_id = $_GET['survey_id'];

    // ดึงข้อมูลคำถามที่เกี่ยวข้องกับ survey_id
    $sql = "SELECT * FROM questions WHERE survey_id = '$survey_id'";
    $result = $conn->query($sql);

    // ตรวจสอบว่ามีคำถามในแบบสอบถามนี้หรือไม่
    if ($result->num_rows > 0) {
        // เริ่มต้นการแสดงผลแบบฟอร์มการประเมิน
        echo "<form method='POST' action='submit_response.php'>";

        // ลูปผ่านคำถามที่ได้
        while ($row = $result->fetch_assoc()) {
            $question_id = $row['question_id'];
            $question_text = $row['question_text'];
            $question_type = $row['question_type'];

            // แสดงคำถามตามประเภท
            echo "<div class='form-group'>";
            echo "<label for='q$question_id'>$question_text</label>";

            if ($question_type == 'multiple_choice') {
                // ดึงตัวเลือกสำหรับคำถามประเภท Multiple Choice
                $sql_options = "SELECT * FROM options WHERE question_id = '$question_id'";
                $options_result = $conn->query($sql_options);

                echo "<select class='form-control' name='q$question_id'>";
                while ($option = $options_result->fetch_assoc()) {
                    echo "<option value='" . $option['option_text'] . "'>" . $option['option_text'] . "</option>";
                }
                echo "</select>";

            } elseif ($question_type == 'rating') {
                // คำถามประเภท Rating (ให้คะแนน)
                echo "<input type='range' class='form-control-range' name='q$question_id' min='1' max='5' step='1'>";

            } elseif ($question_type == 'open_ended') {
                // คำถามประเภท Open-ended (ข้อความ)
                echo "<textarea class='form-control' name='q$question_id' rows='3'></textarea>";
            }

            echo "</div>";
        }

        // ปุ่มส่งข้อมูล
        echo "<button type='submit' class='btn btn-success btn-block'>ส่งคำตอบ</button>";
        echo "</form>";
    } else {
        echo "ไม่มีคำถามในแบบสอบถามนี้";
    }
} else {
    echo "ไม่พบแบบสอบถาม";
}
?>
<!--  -->

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แบบประเมินการสอน</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-8">
    <script>
        // ดึงค่า range และแสดงใน span
        const rangeInput = document.getElementById('q2');
        const rangeValue = document.getElementById('rangeValue');

        // แสดงค่าเริ่มต้น (ค่าที่เลือก) ใน span
        rangeValue.textContent = rangeInput.value;

        // เมื่อค่าใน range เปลี่ยนแปลง ให้แสดงค่าใหม่
        rangeInput.addEventListener('input', function() {
            rangeValue.textContent = rangeInput.value;
        });
    </script>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</body>
</html>
