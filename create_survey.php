<?php
include('db.php');

// ตรวจสอบการส่งข้อมูลแบบฟอร์ม
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = $_POST['title'];
    $description = $_POST['description'];

    // สร้างแบบประเมินใหม่ในฐานข้อมูล
    $sql = "INSERT INTO surveys (title, description) VALUES ('$title', '$description')";

    if ($conn->query($sql) === TRUE) {
        echo "แบบประเมินถูกสร้างเรียบร้อย";
    } else {
        echo "Error: " . $sql . "<br>" . $conn->error;
    }
}
?>

<form method="POST" action="create_survey.php">
    <div class="form-group">
        <label for="title">ชื่อแบบประเมิน:</label>
        <input type="text" class="form-control" name="title" required>
    </div>
    <div class="form-group">
        <label for="description">คำอธิบาย:</label>
        <textarea class="form-control" name="description" required></textarea>
    </div>
    <button type="submit" class="btn btn-primary">สร้างแบบประเมิน</button>
</form>
