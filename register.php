<?php
// Include database connection
include('db.php');

// Initialize session
session_start();

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $email = $_POST['email'];
    $role = $_POST['role'];
    
    // Validate input
    $errors = [];
    
    // Check if username already exists
    $check_sql = "SELECT * FROM users WHERE username = '$username'";
    $result = $conn->query($check_sql);
    if ($result->num_rows > 0) {
        $errors[] = "ຊື່ຜູ້ໃຊ້ນີ້ມີຢູ່ແລ້ວ";
    }
    
    // Check if passwords match
    if ($password !== $confirm_password) {
        $errors[] = "ລະຫັດຜ່ານບໍ່ກົງກັນ";
    }
    
    // If no errors, proceed with registration
    if (empty($errors)) {
        // In production, use password_hash() here
        $sql = "INSERT INTO users (username, password, email, role) VALUES ('$username', '$password', '$email', '$role')";
        
        if ($conn->query($sql) === TRUE) {
            $_SESSION['register_success'] = true;
            header("Location: login.php");
            exit();
        } else {
            $errors[] = "ການລົງທະບຽນລົ້ມເຫລວ: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Teacher Evaluation System</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card mt-5">
                    <div class="card-body">
                        <h3 class="text-center">ລົງທະບຽນ</h3>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul>
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                            <div class="form-group">
                                <label for="username">ຊື່ຜູ້ໃຊ້</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="email">ອີເມວ</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="password">ລະຫັດຜ່ານ</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <div class="form-group">
                                <label for="confirm_password">ຢືນຢັນລະຫັດຜ່ານ</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            <div class="form-group">
                                <label for="role">ບົດບາດ</label>
                                <select class="form-control" id="role" name="role" required>
                                    <option value="student">ນັກສຶກສາ</option>
                                    <option value="teacher">ອາຈານ</option>
                                    <option value="admin">ຜູ້ບໍລິຫານ</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">ລົງທະບຽນ</button>
                            <div class="text-center mt-2">
                                <p>ມີບັນຊີແລ້ວບໍ? <a href="login.php">ເຂົ້າສູ່ລະບົບ</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</body>
</html>