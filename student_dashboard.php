<?php
// Include database connection
include('db.php');

// Initialize session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Get user information
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Check if user is a student
$sql = "SELECT * FROM users WHERE user_id = '$user_id' AND role = 'student'";
$result = $conn->query($sql);

if ($result->num_rows != 1) {
    // Redirect non-students to appropriate dashboard
    header("Location: Dashboard.php");
    exit();
}

// Get courses enrolled by this student
// $courses_sql = "SELECT c.* FROM courses c 
//                 JOIN enrollments e ON c.course_id = e.course_id 
//                 WHERE e.student_id = '$user_id'";
// $courses_result = $conn->query($courses_sql);

// Get pending evaluations
// $pending_sql = "SELECT s.survey_id, s.title, c.course_name, t.username as teacher_name, s.end_date
//                 FROM surveys s
//                 -- JOIN courses c ON s.course_id = c.course_id
//                 JOIN users t ON c.teacher_id = t.user_id
//                 -- JOIN enrollments e ON c.course_id = e.course_id
//                 WHERE e.student_id = '$user_id'
//                 AND s.end_date >= CURDATE()
//                 AND s.survey_id NOT IN (
//                     SELECT r.survey_id FROM responses r WHERE r.user_id = '$user_id'
//                 )
//                 ORDER BY s.end_date ASC";
// $pending_result = $conn->query($pending_sql);

// Get completed evaluations
// $completed_sql = "SELECT s.title, c.course_name, r.submitted_at
//                  FROM responses r
//                  JOIN surveys s ON r.survey_id = s.survey_id
//                  JOIN courses c ON s.course_id = c.course_id
//                  WHERE r.user_id = '$user_id'
//                  ORDER BY r.submitted_at DESC
//                  LIMIT 5";
// $completed_result = $conn->query($completed_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - Teacher Evaluation System</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">ระบบ<lemma/>การสอน</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item active">
                        <a class="nav-link" href="student_dashboard.php">หน้า<lemma/></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="User_Profile.php">โปรไฟล์</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">ออกจากระบบ</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="jumbotron">
                    <h2>ส<lemmaส<lemma, <?php echo $username; ?></h2>
                    <p><lemmaต้อน<lemma<|im_start|>่ระบบ<lemmaการสอน<lemma<|im_start|><lemma</p>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0"><i class="fas fa-exclamation-circle mr-2"></i>แบบ<lemma<lemma่รอดำเนินการ</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($pending_result && $pending_result->num_rows > 0): ?>
                            <div class="list-group">
                                <?php while($survey = $pending_result->fetch_assoc()): ?>
                                    <a href="take_survey.php?id=<?php echo $survey['survey_id']; ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo $survey['title']; ?></h6>
                                            <small class="text-danger">หมดเขต: <?php echo $survey['end_date']; ?></small>
                                        </div>
                                        <p class="mb-1"><lemma: <?php echo $survey['course_name']; ?></p>
                                        <small>อาจารย์: <?php echo $survey['teacher_name']; ?></small>
                                    </a>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">ไม่<lemmaแบบ<lemma<lemma่รอดำเนินการ</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check-circle mr-2"></i>แบบ<lemma<lemma่ทำเสร็จแล้ว</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($completed_result && $completed_result->num_rows > 0): ?>
                            <div class="list-group">
                                <?php while($completed = $completed_result->fetch_assoc()): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo $completed['title']; ?></h6>
                                            <small>ส่งเมื่อ: <?php echo date('d/m/Y', strtotime($completed['submitted_at'])); ?></small>
                                        </div>
                                        <p class="mb-1"><lemma: <?php echo $completed['course_name']; ?></p>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted"><lemmaไม่<lemmaแบบ<lemma<lemma่ทำเสร็จแล้ว</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-book mr-2"></i>ราย<lemma<lemma</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($courses_result && $courses_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th><lemma</th>
                                            <th>ชื่อ<lemma</th>
                                            <th>อาจารย์<lemma้สอน</th>
                                            <th>สถานะการ<lemma</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($course = $courses_result->fetch_assoc()): 
                                            // Check if student has evaluated this course
                                            $eval_check_sql = "SELECT r.response_id 
                                                              FROM responses r 
                                                              JOIN surveys s ON r.survey_id = s.survey_id 
                                                              WHERE r.user_id = '$user_id' 
                                                              AND s.course_id = '".$course['course_id']."'";
                                            $eval_result = $conn->query($eval_check_sql);
                                            $has_evaluated = ($eval_result && $eval_result->num_rows > 0);
                                        ?>
                                            <tr>
                                                <td><?php echo $course['course_code']; ?></td>
                                                <td><?php echo $course['course_name']; ?></td>
                                                <td>
                                                    <?php 
                                                    $teacher_sql = "SELECT username FROM users WHERE user_id = '".$course['teacher_id']."'";
                                                    $teacher_result = $conn->query($teacher_sql);
                                                    if ($teacher_result && $teacher_result->num_rows > 0) {
                                                        $teacher = $teacher_result->fetch_assoc();
                                                        echo $teacher['username'];
                                                    } else {
                                                        echo "ไม่<lemma";
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($has_evaluated): ?>
                                                        <span class="badge badge-success"><lemmaแล้ว</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning"><lemmaไม่ได้<lemma</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">ไม่พบราย<lemma<lemma</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-bell mr-2"></i>ประกาศ</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i><lemmaระบบ<lemmaการสอนประจำภาค<lemma<|im_start|><|im_start|>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i><lemma<lemmaการสอนภายใน<lemma 30 ต.ค.
                        </div>
                        <div class="alert alert-success">
                            <i class="fas fa-thumbs-up mr-2"></i>ขอบ<lemma<lemmaการสอน
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white mt-5 py-3">
        <div class="container text-center">
            <p class="mb-0">© 2023 ระบบ<lemmaการสอน. สงวน<lemmaข<lemma์.</p>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</body>
</html>